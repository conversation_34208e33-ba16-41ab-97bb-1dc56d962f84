import { Redis } from '../../../api/utils/redis'
import { log } from '../../../api/utils/utils'
import { Player } from '../../../api/wrappers/player'

export class SlotManager {
    private currentSlotHost: string | null = null
    private lastManageSlots: number = 0
    private readonly taskType: string

    constructor(taskType: string) {
        this.taskType = taskType
    }

    /**
     * Main slot management method to be called periodically
     * Limited to run every 10 seconds
     */
    public manageSlots(): void {
        const now = Date.now()

        // Limit execution to every 10 seconds
        if (now - this.lastManageSlots < 10000) {
            return
        }
        this.lastManageSlots = now

        // Try to take a slot if we don't have one
        if (!this.hasSlot()) {
            this.tryTakeSlot()
        } else {
            // Send heartbeat and check host availability
            this.sendHeartbeat()
            this.checkHostAvailability()
        }
    }

    /**
     * Check if we currently have a slot
     */
    public hasSlot(): boolean {
        if (!this.currentSlotHost) return false

        try {
            const slaveUsername = Player.local?.username
            if (!slaveUsername) return false

            const slotKey = `delivery:slots:${this.taskType}:${this.currentSlotHost}:${slaveUsername}`
            const keys = Redis.keys(slotKey)
            return keys.length > 0
        } catch (e) {
            log('[Slot Management] Error checking slot:', e)
            return false
        }
    }

    /**
     * Get the current slot host
     */
    public getCurrentSlotHost(): string | null {
        return this.currentSlotHost
    }

    /**
     * Try to take an available slot
     */
    private tryTakeSlot(): void {
        try {
            const slaveUsername = Player.local?.username
            if (!slaveUsername) return

            // Get all available hosts
            const availableHosts = this.getAvailableHosts()

            for (const hostUsername of availableHosts) {
                const slotKey = `delivery:slots:${this.taskType}:${hostUsername}:${slaveUsername}`

                // Try to take the slot with TTL of 60 seconds
                const success = Redis.setnx(slotKey, slaveUsername, 60)

                if (success) {
                    this.currentSlotHost = hostUsername
                    log(`[Slot Management] Successfully took slot for host: ${hostUsername}`)
                    return
                }
            }

            log('[Slot Management] No available slots found')
        } catch (e) {
            log('[Slot Management] Error taking slot:', e)
        }
    }

    /**
     * Get list of available hosts with free slots
     */
    private getAvailableHosts(): string[] {
        try {
            // Get all slot-info keys for this task type
            const pattern = `delivery:slots-info:${this.taskType}:*`
            const keys = Redis.keys(pattern)

            log("Slot infos: [" + keys.join(', ') + ']')
            const availableHosts: string[] = []

            for (const key of keys) {

                // Extract host username from key
                const hostUsername = key.split(':').pop()
                if (!hostUsername) continue

                // Check if this host has available slots
                const slotInfo = Redis.getJson(key)
                if (!slotInfo) continue

                const maxSlots = slotInfo.maxSlots || 1

                // Count current slots taken for this host
                const currentSlotsPattern = `delivery:slots:${this.taskType}:${hostUsername}:*`
                const currentSlots = Redis.keys(currentSlotsPattern).length

                if (currentSlots < maxSlots) {
                    availableHosts.push(hostUsername)
                }
            }

            return availableHosts
        } catch (e) {
            log('[Slot Management] Error getting available hosts:', e)
            return []
        }
    }

    /**
     * Send heartbeat to maintain slot
     */
    private sendHeartbeat(): void {
        if (!this.currentSlotHost) return

        try {
            const slaveUsername = Player.local?.username
            if (!slaveUsername) return

            const slotKey = `delivery:slots:${this.taskType}:${this.currentSlotHost}:${slaveUsername}`

            // Refresh the TTL to 60 seconds
            Redis.set(slotKey, slaveUsername, 60)
            log(`[Slot Management] Heartbeat sent for host: ${this.currentSlotHost}`)
        } catch (e) {
            log('[Slot Management] Error sending heartbeat:', e)
        }
    }

    /**
     * Check if the current host is still available
     */
    private checkHostAvailability(): void {
        if (!this.currentSlotHost) return

        try {
            const slotInfoKey = `delivery:slots-info:${this.taskType}:${this.currentSlotHost}`

            const keys = Redis.keys(slotInfoKey)
            if (keys.length === 0) {
                log(`[Slot Management] Host ${this.currentSlotHost} is no longer available`)
            }
        } catch (e) {
            log('[Slot Management] Error checking host availability:', e)
        }
    }


}
